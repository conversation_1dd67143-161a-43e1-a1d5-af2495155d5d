/* Android 13 Style Wavy Progress Bar */
.wave-progress-container {
  position: fixed;
  top: 0;
  left: 72px; /* Align with main content (side navigation width) */
  width: 0%; /* Start with 0 width */
  max-width: calc(100vw - 72px); /* Maximum width minus side navigation */
  z-index: 1000;
  height: 10px;
  background-color: transparent;
  padding: 2px 0;
  opacity: 0;
  transition: opacity 0.3s ease-out, width 0.1s ease-out;
}

.wave-progress-container.visible {
  opacity: 1;
}

.wave-progress {
  width: 100%;
  height: 100%;
  display: block;
}

.wave-path {
  transition: stroke-dashoffset 0.15s ease-out;
}

.wave-background {
  opacity: 0.4;
}

/* Subtle hover effect */
.wave-progress-container:hover .wave-path {
  filter: drop-shadow(
    0 0 8px rgba(var(--md-sys-color-primary-rgb), 0.7)
  ) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .wave-progress-container {
    left: 0;
    max-width: 100vw;
  }
}

/* For pages without side navigation */
.wave-progress-container.full-width {
  left: 0;
  max-width: 100vw;
}
