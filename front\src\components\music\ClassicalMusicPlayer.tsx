import React, { useState, useRef, useEffect } from 'react';
import '@material/web/all.js';
import './ClassicalMusicPlayer.css';

interface Track {
  id: number;
  title: string;
  artist: string;
  duration: string;
  src: string;
  cover?: string;
  albumCover?: string;
}

const tracks: Track[] = [
  {
    id: 1,
    title: 'Neon Dreams',
    artist: 'Synthwave Collective',
    duration: '3:42',
    src: '/audio/neon-dreams.mp3',
    cover: '🌆',
    albumCover: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop'
  },
  {
    id: 2,
    title: 'Digital Horizon',
    artist: 'Cyber Orchestra',
    duration: '4:15',
    src: '/audio/digital-horizon.mp3',
    cover: '🌌',
    albumCover: 'https://images.unsplash.com/photo-1514320291840-2e0a9bf2a9ae?w=400&h=400&fit=crop'
  },
  {
    id: 3,
    title: 'Electric Pulse',
    artist: 'Future Beats',
    duration: '3:28',
    src: '/audio/electric-pulse.mp3',
    cover: '⚡',
    albumCover: 'https://images.unsplash.com/photo-1571974599782-87624638275c?w=400&h=400&fit=crop'
  }
];

const ClassicalMusicPlayer: React.FC = () => {
  const [currentTrack, setCurrentTrack] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [liked, setLiked] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isRepeat, setIsRepeat] = useState(false);
  const [isShuffle, setIsShuffle] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleEnded = () => {
      if (isRepeat) {
        audio.currentTime = 0;
        audio.play();
      } else if (currentTrack < tracks.length - 1) {
        setCurrentTrack(currentTrack + 1);
      } else {
        setIsPlaying(false);
      }
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleLoadedMetadata = () => {
      setDuration(audio.duration);
    };

    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);

    return () => {
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
    };
  }, [currentTrack, isRepeat]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.volume = 0.7;
    if (isPlaying) {
      audio.play().catch(console.error);
    } else {
      audio.pause();
    }
  }, [isPlaying, currentTrack]);

  const togglePlay = () => setIsPlaying(!isPlaying);

  const nextTrack = () => {
    if (isShuffle) {
      const randomIndex = Math.floor(Math.random() * tracks.length);
      setCurrentTrack(randomIndex);
    } else {
      setCurrentTrack((prev) => (prev + 1) % tracks.length);
    }
  };

  const prevTrack = () => {
    setCurrentTrack((prev) => (prev - 1 + tracks.length) % tracks.length);
  };

  const toggleLike = () => setLiked(!liked);
  const toggleRepeat = () => setIsRepeat(!isRepeat);
  const toggleShuffle = () => setIsShuffle(!isShuffle);

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0;

  return (
    <div className="music-player-card">
      <audio
        ref={audioRef}
        src={tracks[currentTrack]?.src}
        preload="metadata"
      />

      <div className="card-body">
        <div className="player-grid">
          {/* Album Cover Section */}
          <div className="album-cover-section">
            <div className="album-cover-container">
              <img
                src={tracks[currentTrack]?.albumCover || 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop'}
                alt="Album cover"
                className="album-cover-image"
              />
              {/* Audio Visualizer Overlay */}
              <div className={`audio-bars ${isPlaying ? 'playing' : ''}`}>
                <div className="audio-bar"></div>
                <div className="audio-bar"></div>
                <div className="audio-bar"></div>
                <div className="audio-bar"></div>
                <div className="audio-bar"></div>
                <div className="audio-bar"></div>
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="content-section">
            {/* Header with track info and like button */}
            <div className="track-header">
              <div className="track-info">
                <h3 className="playlist-title">Daily Mix</h3>
                <p className="track-count">{tracks.length} Tracks</p>
                <h1 className="track-title">{tracks[currentTrack]?.title}</h1>
                <p className="track-artist">{tracks[currentTrack]?.artist}</p>
              </div>
              <button
                className={`like-button ${liked ? 'liked' : ''}`}
                onClick={toggleLike}
              >
                <md-icon>{liked ? 'favorite' : 'favorite_border'}</md-icon>
              </button>
            </div>

            {/* Progress Section */}
            <div className="progress-section">
              <div className="progress-bar-container">
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{ width: `${progressPercentage}%` }}
                  ></div>
                </div>
              </div>
              <div className="time-display">
                <span className="current-time">{formatTime(currentTime)}</span>
                <span className="total-time">{formatTime(duration)}</span>
              </div>
            </div>

            {/* Controls Section */}
            <div className="controls-section">
              <button
                className={`control-button ${isRepeat ? 'active' : ''}`}
                onClick={toggleRepeat}
              >
                <md-icon>repeat_one</md-icon>
              </button>
              <button
                className="control-button"
                onClick={prevTrack}
              >
                <md-icon>skip_previous</md-icon>
              </button>
              <button
                className="play-button"
                onClick={togglePlay}
              >
                <md-icon className="play-icon">{isPlaying ? 'pause_circle' : 'play_circle'}</md-icon>
              </button>
              <button
                className="control-button"
                onClick={nextTrack}
              >
                <md-icon>skip_next</md-icon>
              </button>
              <button
                className={`control-button ${isShuffle ? 'active' : ''}`}
                onClick={toggleShuffle}
              >
                <md-icon>shuffle</md-icon>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClassicalMusicPlayer;
