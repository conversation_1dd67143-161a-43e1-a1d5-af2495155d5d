# Admin API Documentation

This document describes the complete Admin API for the Cyrus Blog backend, designed to support the React admin frontend.

## Authentication

### Login
```http
POST /api/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123456"
}
```

**Response:**
```json
{
  "success": true,
  "token": "admin123456",
  "message": "Login successful",
  "user": {
    "id": "1",
    "username": "admin",
    "email": "<EMAIL>",
    "role": "admin",
    "lastLogin": "2024-01-01T00:00:00Z"
  }
}
```

## Dashboard

### Get Dashboard Statistics
```http
GET /api/admin/dashboard/stats
Authorization: Bearer {token}
```

**Response:**
```json
{
  "totalPosts": 10,
  "totalCategories": 5,
  "totalViews": 1234,
  "recentPosts": 3
}
```

## Blog Post Management

### Get All Posts
```http
GET /api/admin/posts?search=query&category=React&status=published&page=1&limit=20
Authorization: Bearer {token}
```

**Response:**
```json
[
  {
    "id": 1,
    "title": "Sample Post",
    "excerpt": "This is a sample post",
    "content": "Full content here...",
    "slug": "sample-post",
    "publishDate": "2024-01-01T00:00:00Z",
    "author": "Admin",
    "readTime": 5,
    "category": "React",
    "tags": ["react", "javascript"],
    "featured": false,
    "status": "published"
  }
]
```

### Get Single Post
```http
GET /api/admin/posts/{id}
Authorization: Bearer {token}
```

### Create Post
```http
POST /api/admin/posts
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "New Post",
  "excerpt": "Post excerpt",
  "content": "Full post content",
  "category": "React",
  "tags": ["react", "tutorial"],
  "status": "published",
  "author": "Admin",
  "readTime": 5,
  "featured": false
}
```

### Update Post
```http
PUT /api/admin/posts/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "title": "Updated Title",
  "status": "draft"
}
```

### Delete Post
```http
DELETE /api/admin/posts/{id}
Authorization: Bearer {token}
```

### Bulk Delete Posts
```http
POST /api/admin/posts/bulk-delete
Authorization: Bearer {token}
Content-Type: application/json

{
  "ids": [1, 2, 3]
}
```

### Publish Post
```http
POST /api/admin/posts/{id}/publish
Authorization: Bearer {token}
```

### Unpublish Post
```http
POST /api/admin/posts/{id}/unpublish
Authorization: Bearer {token}
```

## Music Management

### Get All Music Tracks
```http
GET /api/admin/music?search=query&genre=Rock&page=1&limit=20
Authorization: Bearer {token}
```

**Response:**
```json
[
  {
    "id": 1,
    "title": "Song Title",
    "artist": "Artist Name",
    "album": "Album Name",
    "duration": 240,
    "file_url": "/uploads/music/song.mp3",
    "cover_url": "/uploads/covers/cover.jpg",
    "genre": "Rock",
    "file_size": 5242880,
    "status": "active",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
]
```

### Get Single Music Track
```http
GET /api/admin/music/{id}
Authorization: Bearer {token}
```

### Upload Music
```http
POST /api/admin/music/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

Form fields:
- title: string (required)
- artist: string (required)
- album: string (optional)
- genre: string (optional)
- music: file (required) - audio file
- cover: file (optional) - cover image
```

### Delete Music Track
```http
DELETE /api/admin/music/{id}
Authorization: Bearer {token}
```

### Bulk Delete Music Tracks
```http
POST /api/admin/music/bulk-delete
Authorization: Bearer {token}
Content-Type: application/json

{
  "ids": [1, 2, 3]
}
```

## File Upload

### Generic File Upload
```http
POST /api/admin/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

Form fields:
- file: file (required)
- type: string (optional) - "image", "audio", or "document"
```

**Response:**
```json
{
  "url": "/uploads/images/file.jpg",
  "filename": "file.jpg",
  "size": 1024000,
  "mimeType": "image/jpeg"
}
```

## Error Responses

All endpoints may return error responses in the following format:

```json
{
  "error": "Error message",
  "details": "Additional error details"
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request
- `401` - Unauthorized
- `404` - Not Found
- `500` - Internal Server Error

## Testing

Run the test script to verify all endpoints:

```bash
cd backend
python test_admin_api.py
```

Make sure the backend server is running on `localhost:3000` before running tests.

## Database Schema

The API uses the following main tables:

### blog_posts
- `id` - Primary key
- `title` - Post title
- `excerpt` - Short description
- `content` - Full content
- `slug` - URL slug
- `date` - Publication date
- `author` - Author name
- `read_time` - Estimated read time in minutes
- `category` - Single category
- `tags` - JSON array of tags
- `featured` - Boolean flag
- `status` - "draft" or "published"
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp

### music_tracks
- `id` - Primary key
- `title` - Track title
- `artist` - Artist name
- `album` - Album name (optional)
- `duration` - Duration in seconds
- `file_url` - URL to audio file
- `cover_url` - URL to cover image (optional)
- `genre` - Music genre (optional)
- `file_size` - File size in bytes
- `status` - "active", "processing", or "error"
- `created_at` - Creation timestamp
- `updated_at` - Last update timestamp
