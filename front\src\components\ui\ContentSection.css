/* Content Section - Material Design 3 Official Style */

.content-section {
  padding: var(--md-sys-spacing-8) 0;
  max-width: 1200px;
  margin: 0 auto;
}

.content-section-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--md-sys-spacing-6);
  padding: 0 var(--md-sys-spacing-4);
}

.content-card {
  --md-elevated-card-container-color: rgba(255, 255, 255, 0.05);
  --md-elevated-card-container-elevation: var(--md-sys-elevation-level2);
  
  border-radius: var(--md-sys-shape-corner-extra-large);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all var(--md-sys-motion-duration-medium2) var(--md-sys-motion-easing-emphasized);
  overflow: hidden;
  position: relative;
}

.content-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #6750a4, #7d5260, #625b71);
  opacity: 0;
  transition: opacity var(--md-sys-motion-duration-short2) var(--md-sys-motion-easing-standard);
}

.content-card:hover {
  --md-elevated-card-container-elevation: var(--md-sys-elevation-level4);
  transform: translateY(-8px);
  border-color: rgba(255, 255, 255, 0.2);
}

.content-card:hover::before {
  opacity: 1;
}

.content-card-content {
  padding: var(--md-sys-spacing-6);
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-4);
  height: 100%;
}

.content-card-icon {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, rgba(103, 80, 164, 0.2), rgba(125, 82, 96, 0.2));
  border-radius: var(--md-sys-shape-corner-large);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.content-card-icon md-icon {
  color: rgba(255, 255, 255, 0.9);
  font-size: 28px !important;
}

.content-card-title {
  font-size: var(--md-sys-typescale-headline-small-size);
  font-weight: var(--md-sys-typescale-headline-small-weight);
  line-height: var(--md-sys-typescale-headline-small-line-height);
  color: rgba(255, 255, 255, 0.95);
  margin: 0;
  font-family: "Roboto", sans-serif;
}

.content-card-description {
  font-size: var(--md-sys-typescale-body-large-size);
  font-weight: var(--md-sys-typescale-body-large-weight);
  line-height: var(--md-sys-typescale-body-large-line-height);
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  flex: 1;
  font-family: "Roboto", sans-serif;
}

.content-card-action {
  margin-top: auto;
  padding-top: var(--md-sys-spacing-2);
}

.content-card-action md-text-button {
  --md-text-button-label-text-color: rgba(208, 188, 255, 0.9);
  --md-text-button-icon-color: rgba(208, 188, 255, 0.9);
  --md-text-button-hover-state-layer-color: rgba(208, 188, 255, 0.08);
  --md-text-button-focus-state-layer-color: rgba(208, 188, 255, 0.12);
  --md-text-button-pressed-state-layer-color: rgba(208, 188, 255, 0.12);
}

/* Animation for cards */
.content-card {
  animation: cardEnter var(--md-sys-motion-duration-medium3) var(--md-sys-motion-easing-emphasized);
}

@keyframes cardEnter {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animation for multiple cards */
.content-card:nth-child(1) { animation-delay: 0ms; }
.content-card:nth-child(2) { animation-delay: 100ms; }
.content-card:nth-child(3) { animation-delay: 200ms; }
.content-card:nth-child(4) { animation-delay: 300ms; }
.content-card:nth-child(5) { animation-delay: 400ms; }
.content-card:nth-child(6) { animation-delay: 500ms; }

/* Responsive Design */
@media (max-width: 768px) {
  .content-section {
    padding: var(--md-sys-spacing-6) 0;
  }
  
  .content-section-grid {
    grid-template-columns: 1fr;
    gap: var(--md-sys-spacing-4);
    padding: 0 var(--md-sys-spacing-3);
  }
  
  .content-card-content {
    padding: var(--md-sys-spacing-5);
    gap: var(--md-sys-spacing-3);
  }
  
  .content-card-icon {
    width: 48px;
    height: 48px;
  }
  
  .content-card-icon md-icon {
    font-size: 24px !important;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .content-card {
    animation: none;
  }
  
  .content-card:hover {
    transform: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .content-card {
    border: 2px solid rgba(255, 255, 255, 0.3);
  }
  
  .content-card-title {
    color: white;
  }
  
  .content-card-description {
    color: rgba(255, 255, 255, 0.9);
  }
}
