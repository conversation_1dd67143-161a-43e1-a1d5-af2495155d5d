/* Notification Container Styles */

.notification-container {
  position: fixed;
  top: 80px;
  right: 24px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  width: 100%;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16px;
  border-radius: 12px;
  box-shadow: var(--md-sys-elevation-level3);
  animation: slideIn 0.3s ease-out;
  border-left: 4px solid;
}

.notification-success {
  background: var(--md-sys-color-primary-container);
  color: var(--md-sys-color-on-primary-container);
  border-left-color: var(--md-sys-color-primary);
}

.notification-error {
  background: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
  border-left-color: var(--md-sys-color-error);
}

.notification-warning {
  background: var(--md-sys-color-tertiary-container);
  color: var(--md-sys-color-on-tertiary-container);
  border-left-color: var(--md-sys-color-tertiary);
}

.notification-info {
  background: var(--md-sys-color-secondary-container);
  color: var(--md-sys-color-on-secondary-container);
  border-left-color: var(--md-sys-color-secondary);
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  flex: 1;
}

.notification-icon {
  font-size: 24px;
  margin-top: 2px;
}

.notification-success .notification-icon {
  color: var(--md-sys-color-primary);
}

.notification-error .notification-icon {
  color: var(--md-sys-color-error);
}

.notification-warning .notification-icon {
  color: var(--md-sys-color-tertiary);
}

.notification-info .notification-icon {
  color: var(--md-sys-color-secondary);
}

.notification-text {
  flex: 1;
}

.notification-title {
  margin: 0 0 4px 0;
  font-weight: 500;
  line-height: 1.3;
}

.notification-message {
  margin: 0;
  line-height: 1.4;
  opacity: 0.9;
}

.notification-close {
  --md-icon-button-icon-size: 20px;
  --md-icon-button-size: 32px;
  margin-top: -4px;
  margin-right: -4px;
}

.notification-success .notification-close {
  --md-icon-button-icon-color: var(--md-sys-color-on-primary-container);
}

.notification-error .notification-close {
  --md-icon-button-icon-color: var(--md-sys-color-on-error-container);
}

.notification-warning .notification-close {
  --md-icon-button-icon-color: var(--md-sys-color-on-tertiary-container);
}

.notification-info .notification-close {
  --md-icon-button-icon-color: var(--md-sys-color-on-secondary-container);
}

/* Animations */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.notification-item.removing {
  animation: slideOut 0.3s ease-in forwards;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-container {
    top: 70px;
    right: 16px;
    left: 16px;
    max-width: none;
  }
  
  .notification-item {
    padding: 12px;
  }
  
  .notification-icon {
    font-size: 20px;
  }
  
  .notification-title {
    font-size: 14px;
  }
  
  .notification-message {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .notification-container {
    top: 60px;
    right: 8px;
    left: 8px;
  }
  
  .notification-item {
    padding: 10px;
  }
  
  .notification-content {
    gap: 8px;
  }
}

/* Hover Effects */
.notification-item:hover {
  transform: translateY(-1px);
  box-shadow: var(--md-sys-elevation-level4);
}

.notification-close:hover {
  background: rgba(0, 0, 0, 0.1);
}
