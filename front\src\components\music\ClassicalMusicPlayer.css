/* HeroUI-style Music Player Card */
.music-player-card {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  font-family: "Inter", "Roboto", sans-serif;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(16px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
}

/* Hide default audio controls */
audio {
  display: none;
}

.card-body {
  padding: 24px;
}

.player-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
  align-items: center;
}

/* Album Cover Section */
.album-cover-section {
  position: relative;
}

.album-cover-container {
  position: relative;
  width: 100%;
  aspect-ratio: 1;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.album-cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.album-cover-image:hover {
  transform: scale(1.05);
}

/* Audio Visualizer Overlay */
.audio-bars {
  position: absolute;
  bottom: 12px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 3px;
  opacity: 0;
  transition: opacity 0.3s ease;
  background: rgba(0, 0, 0, 0.3);
  padding: 8px 12px;
  border-radius: 20px;
  backdrop-filter: blur(8px);
}

.audio-bars.playing {
  opacity: 1;
}

.audio-bar {
  width: 3px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  animation: audioWave 1.5s ease-in-out infinite;
}

.audio-bar:nth-child(1) {
  height: 12px;
  animation-delay: 0s;
}
.audio-bar:nth-child(2) {
  height: 20px;
  animation-delay: 0.2s;
}
.audio-bar:nth-child(3) {
  height: 16px;
  animation-delay: 0.4s;
}
.audio-bar:nth-child(4) {
  height: 24px;
  animation-delay: 0.6s;
}
.audio-bar:nth-child(5) {
  height: 18px;
  animation-delay: 0.8s;
}
.audio-bar:nth-child(6) {
  height: 14px;
  animation-delay: 1s;
}

@keyframes audioWave {
  0%,
  100% {
    transform: scaleY(0.3);
  }
  50% {
    transform: scaleY(1);
  }
}

/* Content Section */
.content-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Track Header */
.track-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.track-info {
  flex: 1;
}

.playlist-title {
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 4px 0;
}

.track-count {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0 0 8px 0;
}

.track-title {
  font-size: 20px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.95);
  margin: 0 0 4px 0;
  letter-spacing: -0.01em;
}

.track-artist {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  font-weight: 400;
}

/* Like Button */
.like-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  transform: translateY(-8px) translateX(8px);
}

.like-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.like-button.liked {
  color: #ff6b6b;
}

/* Progress Section */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.progress-bar-container {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  transition: width 0.1s ease;
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

/* Controls Section */
.controls-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
}

.control-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.control-button.active {
  color: rgba(255, 255, 255, 0.9);
}

.play-button {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-button:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.play-button:active {
  transform: scale(0.95);
}

.play-icon {
  font-size: 54px !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .player-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .album-cover-section {
    max-width: 300px;
    margin: 0 auto;
  }

  .track-header {
    text-align: center;
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  .like-button {
    transform: none;
  }
}

@media (max-width: 480px) {
  .music-player-card {
    max-width: 100%;
    margin: 0 16px;
  }

  .card-body {
    padding: 16px;
  }

  .player-grid {
    gap: 16px;
  }

  .album-cover-section {
    max-width: 250px;
  }

  .track-title {
    font-size: 18px;
  }

  .track-artist {
    font-size: 12px;
  }

  .controls-section {
    gap: 4px;
  }

  .play-icon {
    font-size: 48px !important;
  }
}
