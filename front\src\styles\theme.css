/* Material Design 3 Theme Configuration */

/* Import Material Web Typography */
@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");

/* Material Design 3 Design Tokens - Purple Theme (Based on Official MD3 Screenshots) */
:root {
  /* Primary Color Palette - Modern Purple Theme */
  --md-sys-color-primary: #6750a4;
  --md-sys-color-on-primary: #ffffff;
  --md-sys-color-primary-container: #eaddff;
  --md-sys-color-on-primary-container: #21005d;
  --md-sys-color-primary-fixed: #eaddff;
  --md-sys-color-on-primary-fixed: #21005d;
  --md-sys-color-primary-fixed-dim: #d0bcff;
  --md-sys-color-on-primary-fixed-variant: #4f378b;

  /* Secondary Color Palette - Enhanced */
  --md-sys-color-secondary: #625b71;
  --md-sys-color-on-secondary: #ffffff;
  --md-sys-color-secondary-container: #e8def8;
  --md-sys-color-on-secondary-container: #1d192b;
  --md-sys-color-secondary-fixed: #e8def8;
  --md-sys-color-on-secondary-fixed: #1d192b;
  --md-sys-color-secondary-fixed-dim: #ccc2dc;
  --md-sys-color-on-secondary-fixed-variant: #4a4458;

  /* Tertiary Color Palette - Refined */
  --md-sys-color-tertiary: #7d5260;
  --md-sys-color-on-tertiary: #ffffff;
  --md-sys-color-tertiary-container: #ffd8e4;
  --md-sys-color-on-tertiary-container: #31111d;
  --md-sys-color-tertiary-fixed: #ffd8e4;
  --md-sys-color-on-tertiary-fixed: #31111d;
  --md-sys-color-tertiary-fixed-dim: #efb8c8;
  --md-sys-color-on-tertiary-fixed-variant: #633b48;

  /* Error Color Palette */
  --md-sys-color-error: #ba1a1a;
  --md-sys-color-on-error: #ffffff;
  --md-sys-color-error-container: #ffdad6;
  --md-sys-color-on-error-container: #410002;

  /* Surface Color Palette - Enhanced */
  --md-sys-color-surface: #fffbfe;
  --md-sys-color-on-surface: #1c1b1f;
  --md-sys-color-surface-variant: #e7e0ec;
  --md-sys-color-on-surface-variant: #49454f;
  --md-sys-color-surface-container-lowest: #ffffff;
  --md-sys-color-surface-container-low: #f7f2fa;
  --md-sys-color-surface-container: #f3edf7;
  --md-sys-color-surface-container-high: #ece6f0;
  --md-sys-color-surface-container-highest: #e6e0e9;
  --md-sys-color-surface-dim: #ded8e1;
  --md-sys-color-surface-bright: #fffbfe;
  --md-sys-color-surface-tint: #6750a4;

  /* Background Colors */
  --md-sys-color-background: #fffbfe;
  --md-sys-color-on-background: #1c1b1f;

  /* Outline Colors */
  --md-sys-color-outline: #79747e;
  --md-sys-color-outline-variant: #cac4d0;

  /* Inverse Colors */
  --md-sys-color-inverse-surface: #313033;
  --md-sys-color-inverse-on-surface: #f4eff4;
  --md-sys-color-inverse-primary: #d0bcff;

  /* Shadow and Scrim */
  --md-sys-color-shadow: #000000;
  --md-sys-color-scrim: #000000;

  /* State Layer Colors */
  --md-sys-color-primary-hover: rgba(103, 80, 164, 0.08);
  --md-sys-color-primary-focus: rgba(103, 80, 164, 0.12);
  --md-sys-color-primary-pressed: rgba(103, 80, 164, 0.12);
  --md-sys-color-primary-dragged: rgba(103, 80, 164, 0.16);

  /* Surface Tint and Overlay */
  --md-sys-color-surface-tint-color: #6750a4;
  --md-sys-color-overlay: rgba(0, 0, 0, 0.5);

  /* Typography Scale */
  --md-sys-typescale-display-large-font: "Roboto";
  --md-sys-typescale-display-large-weight: 400;
  --md-sys-typescale-display-large-size: 57px;
  --md-sys-typescale-display-large-line-height: 64px;

  --md-sys-typescale-display-medium-font: "Roboto";
  --md-sys-typescale-display-medium-weight: 400;
  --md-sys-typescale-display-medium-size: 45px;
  --md-sys-typescale-display-medium-line-height: 52px;

  --md-sys-typescale-display-small-font: "Roboto";
  --md-sys-typescale-display-small-weight: 400;
  --md-sys-typescale-display-small-size: 36px;
  --md-sys-typescale-display-small-line-height: 44px;

  --md-sys-typescale-headline-large-font: "Roboto";
  --md-sys-typescale-headline-large-weight: 400;
  --md-sys-typescale-headline-large-size: 32px;
  --md-sys-typescale-headline-large-line-height: 40px;

  --md-sys-typescale-headline-medium-font: "Roboto";
  --md-sys-typescale-headline-medium-weight: 400;
  --md-sys-typescale-headline-medium-size: 28px;
  --md-sys-typescale-headline-medium-line-height: 36px;

  --md-sys-typescale-headline-small-font: "Roboto";
  --md-sys-typescale-headline-small-weight: 400;
  --md-sys-typescale-headline-small-size: 24px;
  --md-sys-typescale-headline-small-line-height: 32px;

  --md-sys-typescale-title-large-font: "Roboto";
  --md-sys-typescale-title-large-weight: 400;
  --md-sys-typescale-title-large-size: 22px;
  --md-sys-typescale-title-large-line-height: 28px;

  --md-sys-typescale-title-medium-font: "Roboto";
  --md-sys-typescale-title-medium-weight: 500;
  --md-sys-typescale-title-medium-size: 16px;
  --md-sys-typescale-title-medium-line-height: 24px;

  --md-sys-typescale-title-small-font: "Roboto";
  --md-sys-typescale-title-small-weight: 500;
  --md-sys-typescale-title-small-size: 14px;
  --md-sys-typescale-title-small-line-height: 20px;

  --md-sys-typescale-body-large-font: "Roboto";
  --md-sys-typescale-body-large-weight: 400;
  --md-sys-typescale-body-large-size: 16px;
  --md-sys-typescale-body-large-line-height: 24px;

  --md-sys-typescale-body-medium-font: "Roboto";
  --md-sys-typescale-body-medium-weight: 400;
  --md-sys-typescale-body-medium-size: 14px;
  --md-sys-typescale-body-medium-line-height: 20px;

  --md-sys-typescale-body-small-font: "Roboto";
  --md-sys-typescale-body-small-weight: 400;
  --md-sys-typescale-body-small-size: 12px;
  --md-sys-typescale-body-small-line-height: 16px;

  --md-sys-typescale-label-large-font: "Roboto";
  --md-sys-typescale-label-large-weight: 500;
  --md-sys-typescale-label-large-size: 14px;
  --md-sys-typescale-label-large-line-height: 20px;

  --md-sys-typescale-label-medium-font: "Roboto";
  --md-sys-typescale-label-medium-weight: 500;
  --md-sys-typescale-label-medium-size: 12px;
  --md-sys-typescale-label-medium-line-height: 16px;

  --md-sys-typescale-label-small-font: "Roboto";
  --md-sys-typescale-label-small-weight: 500;
  --md-sys-typescale-label-small-size: 11px;
  --md-sys-typescale-label-small-line-height: 16px;

  /* Elevation Shadows */
  --md-sys-elevation-level0: none;
  --md-sys-elevation-level1: 0px 1px 2px 0px rgba(0, 0, 0, 0.3),
    0px 1px 3px 1px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level2: 0px 1px 2px 0px rgba(0, 0, 0, 0.3),
    0px 2px 6px 2px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level3: 0px 1px 3px 0px rgba(0, 0, 0, 0.3),
    0px 4px 8px 3px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level4: 0px 2px 3px 0px rgba(0, 0, 0, 0.3),
    0px 6px 10px 4px rgba(0, 0, 0, 0.15);
  --md-sys-elevation-level5: 0px 4px 4px 0px rgba(0, 0, 0, 0.3),
    0px 8px 12px 6px rgba(0, 0, 0, 0.15);

  /* Shape Tokens */
  --md-sys-shape-corner-none: 0px;
  --md-sys-shape-corner-extra-small: 4px;
  --md-sys-shape-corner-small: 8px;
  --md-sys-shape-corner-medium: 12px;
  --md-sys-shape-corner-large: 16px;
  --md-sys-shape-corner-extra-large: 28px;
  --md-sys-shape-corner-full: 50%;

  /* Motion Tokens */
  --md-sys-motion-duration-short1: 50ms;
  --md-sys-motion-duration-short2: 100ms;
  --md-sys-motion-duration-short3: 150ms;
  --md-sys-motion-duration-short4: 200ms;
  --md-sys-motion-duration-medium1: 250ms;
  --md-sys-motion-duration-medium2: 300ms;
  --md-sys-motion-duration-medium3: 350ms;
  --md-sys-motion-duration-medium4: 400ms;
  --md-sys-motion-duration-long1: 450ms;
  --md-sys-motion-duration-long2: 500ms;
  --md-sys-motion-duration-long3: 550ms;
  --md-sys-motion-duration-long4: 600ms;

  --md-sys-motion-easing-linear: cubic-bezier(0, 0, 1, 1);
  --md-sys-motion-easing-standard: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-standard-accelerate: cubic-bezier(0.3, 0, 1, 1);
  --md-sys-motion-easing-standard-decelerate: cubic-bezier(0, 0, 0, 1);
  --md-sys-motion-easing-emphasized: cubic-bezier(0.2, 0, 0, 1);
  --md-sys-motion-easing-emphasized-accelerate: cubic-bezier(0.05, 0.7, 0.1, 1);
  --md-sys-motion-easing-emphasized-decelerate: cubic-bezier(0.3, 0, 0.8, 0.15);

  /* Layout Breakpoints */
  --md-sys-breakpoint-compact: 600px;
  --md-sys-breakpoint-medium: 840px;
  --md-sys-breakpoint-expanded: 1200px;
  --md-sys-breakpoint-large: 1600px;

  /* Spacing Scale */
  --md-sys-spacing-0: 0px;
  --md-sys-spacing-1: 4px;
  --md-sys-spacing-2: 8px;
  --md-sys-spacing-3: 12px;
  --md-sys-spacing-4: 16px;
  --md-sys-spacing-5: 20px;
  --md-sys-spacing-6: 24px;
  --md-sys-spacing-7: 28px;
  --md-sys-spacing-8: 32px;
  --md-sys-spacing-9: 36px;
  --md-sys-spacing-10: 40px;
  --md-sys-spacing-11: 44px;
  --md-sys-spacing-12: 48px;
}

/* Dark Theme - Modern Purple Theme (Material Design 3 2024) */
@media (prefers-color-scheme: dark) {
  :root {
    /* Primary Color Palette - Dark Purple Enhanced */
    --md-sys-color-primary: #d0bcff;
    --md-sys-color-on-primary: #381e72;
    --md-sys-color-primary-container: #4f378b;
    --md-sys-color-on-primary-container: #eaddff;
    --md-sys-color-primary-fixed: #eaddff;
    --md-sys-color-on-primary-fixed: #21005d;
    --md-sys-color-primary-fixed-dim: #d0bcff;
    --md-sys-color-on-primary-fixed-variant: #4f378b;

    /* Secondary Color Palette - Dark Purple */
    --md-sys-color-secondary: #ccc2dc;
    --md-sys-color-on-secondary: #332d41;
    --md-sys-color-secondary-container: #4a4458;
    --md-sys-color-on-secondary-container: #e8def8;

    /* Tertiary Color Palette - Dark Purple */
    --md-sys-color-tertiary: #efb8c8;
    --md-sys-color-on-tertiary: #492532;
    --md-sys-color-tertiary-container: #633b48;
    --md-sys-color-on-tertiary-container: #ffd8e4;

    /* Error Color Palette - Dark */
    --md-sys-color-error: #ffb4ab;
    --md-sys-color-on-error: #690005;
    --md-sys-color-error-container: #93000a;
    --md-sys-color-on-error-container: #ffdad6;

    /* Surface Color Palette - Dark */
    --md-sys-color-surface: #10090d;
    --md-sys-color-on-surface: #e6e0e9;
    --md-sys-color-surface-variant: #49454f;
    --md-sys-color-on-surface-variant: #cac4d0;
    --md-sys-color-surface-container-lowest: #0b0408;
    --md-sys-color-surface-container-low: #1d1b20;
    --md-sys-color-surface-container: #211f26;
    --md-sys-color-surface-container-high: #2b2930;
    --md-sys-color-surface-container-highest: #36343b;

    /* Background Colors - Dark */
    --md-sys-color-background: #10090d;
    --md-sys-color-on-background: #e6e0e9;

    /* Outline Colors - Dark */
    --md-sys-color-outline: #938f99;
    --md-sys-color-outline-variant: #49454f;

    /* Inverse Colors - Dark */
    --md-sys-color-inverse-surface: #e6e0e9;
    --md-sys-color-inverse-on-surface: #313033;
    --md-sys-color-inverse-primary: #6750a4;
  }
}
