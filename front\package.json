{"name": "front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@material/web": "^2.3.0", "@rollup/plugin-node-resolve": "^16.0.1", "@types/react-router-dom": "^5.3.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-router-dom": "^7.7.1", "rollup": "^4.46.2"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}, "description": "This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.", "main": "eslint.config.js", "keywords": [], "author": "", "license": "ISC"}