/* App-specific styles */

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Utility classes for the app */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: var(--md-sys-shape-corner-small);
  z-index: 1000;
  transition: top var(--md-sys-motion-duration-short4)
    var(--md-sys-motion-easing-standard);
}

.skip-link:focus {
  top: 6px;
}

/* Focus management */
.app:focus-within {
  outline: none;
}

/* Print styles */
@media print {
  .app {
    background: white;
    color: black;
  }
}
