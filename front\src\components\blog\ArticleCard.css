/* Article Card Styles */

.article-card {
  --md-elevated-card-container-color: var(--md-sys-color-surface-container-low);
  --md-elevated-card-container-elevation: var(--md-sys-elevation-level1);

  display: flex;
  flex-direction: column;
  cursor: pointer;
  transition: all var(--md-sys-motion-duration-medium2)
    var(--md-sys-motion-easing-emphasized);
  border-radius: var(--md-sys-shape-corner-large);
  overflow: hidden;
  height: 100%;
  position: relative;
  backdrop-filter: blur(8px);
}

.article-card:hover {
  --md-elevated-card-container-color: var(--md-sys-color-surface-container);
  --md-elevated-card-container-elevation: var(--md-sys-elevation-level4);
  transform: translateY(-4px) scale(1.01);
  box-shadow: var(--md-sys-elevation-level4);
}

.article-card:focus {
  outline: 2px solid var(--md-sys-color-primary);
  outline-offset: 2px;
}

.article-card:active {
  --md-elevated-card-container-elevation: var(--md-sys-elevation-level1);
  transform: translateY(0);
}

/* Featured Article Card */
.article-card-featured {
  --md-elevated-card-container-color: var(--md-sys-color-primary-container);
  border: 2px solid var(--md-sys-color-primary);
}

.article-card-featured .article-card-title {
  color: var(--md-sys-color-on-primary-container);
}

.article-card-featured .article-card-excerpt {
  color: var(--md-sys-color-on-primary-container);
}

/* Article Card Media */
.article-card-media {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
  background-color: var(--md-sys-color-surface-variant);
}

.article-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--md-sys-motion-duration-medium3)
    var(--md-sys-motion-easing-standard);
}

.article-card:hover .article-card-image {
  transform: scale(1.05);
}

.article-card-featured-badge {
  position: absolute;
  top: var(--md-sys-spacing-2);
  right: var(--md-sys-spacing-2);
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-1);
  background-color: var(--md-sys-color-primary);
  color: var(--md-sys-color-on-primary);
  padding: var(--md-sys-spacing-1) var(--md-sys-spacing-2);
  border-radius: var(--md-sys-shape-corner-full);
  box-shadow: var(--md-sys-elevation-level2);
}

.article-card-featured-badge md-icon {
  font-size: 16px;
}

/* Article Card Content */
.article-card-content {
  display: flex;
  flex-direction: column;
  gap: var(--md-sys-spacing-3);
  padding: var(--md-sys-spacing-4);
  flex: 1;
}

.article-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--md-sys-spacing-2);
}

.article-card-category {
  --md-assist-chip-container-color: var(--md-sys-color-secondary-container);
  --md-assist-chip-label-text-color: var(--md-sys-color-on-secondary-container);
  --md-assist-chip-icon-color: var(--md-sys-color-on-secondary-container);
  flex-shrink: 0;
}

.article-card-meta {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-2);
  color: var(--md-sys-color-on-surface-variant);
}

.article-card-date,
.article-card-read-time {
  white-space: nowrap;
}

.article-card-date::after {
  content: "•";
  margin: 0 var(--md-sys-spacing-1);
  color: var(--md-sys-color-outline);
}

.article-card-title {
  margin: 0;
  color: var(--md-sys-color-on-surface);
  font-weight: 500;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.article-card-excerpt {
  margin: 0;
  color: var(--md-sys-color-on-surface-variant);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}

/* Article Card Tags */
.article-card-tags {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-1);
  flex-wrap: wrap;
}

.article-card-tag {
  --md-filter-chip-container-color: var(
    --md-sys-color-surface-container-highest
  );
  --md-filter-chip-label-text-color: var(--md-sys-color-on-surface-variant);
  --md-filter-chip-disabled-container-color: var(
    --md-sys-color-surface-container-highest
  );
  --md-filter-chip-disabled-label-text-color: var(
    --md-sys-color-on-surface-variant
  );
  font-size: var(--md-sys-typescale-label-small-size);
}

.article-card-more-tags {
  color: var(--md-sys-color-on-surface-variant);
  font-style: italic;
}

/* Article Card Footer */
.article-card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  padding-top: var(--md-sys-spacing-2);
  border-top: 1px solid var(--md-sys-color-outline-variant);
}

.article-card-author {
  display: flex;
  align-items: center;
  gap: var(--md-sys-spacing-1);
  color: var(--md-sys-color-on-surface-variant);
}

.article-card-author-icon {
  font-size: 16px;
}

.article-card-author-name {
  font-weight: 500;
}

.article-card-action {
  --md-icon-button-icon-color: var(--md-sys-color-primary);
  --md-icon-button-state-layer-color: var(--md-sys-color-primary);
}

/* Responsive Design */
@media (max-width: 600px) {
  .article-card-content {
    padding: var(--md-sys-spacing-3);
    gap: var(--md-sys-spacing-2);
  }

  .article-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--md-sys-spacing-1);
  }

  .article-card-meta {
    align-self: stretch;
    justify-content: space-between;
  }

  .article-card-media {
    height: 160px;
  }

  .article-card-tags {
    gap: var(--md-sys-spacing-1);
  }
}

/* Animation for card entrance */
.article-card {
  animation: cardSlideIn var(--md-sys-motion-duration-medium4)
    var(--md-sys-motion-easing-emphasized);
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(24px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stagger animation for multiple cards */
.article-card:nth-child(1) {
  animation-delay: 0ms;
}
.article-card:nth-child(2) {
  animation-delay: 100ms;
}
.article-card:nth-child(3) {
  animation-delay: 200ms;
}
.article-card:nth-child(4) {
  animation-delay: 300ms;
}
.article-card:nth-child(5) {
  animation-delay: 400ms;
}
.article-card:nth-child(6) {
  animation-delay: 500ms;
}

/* Dark Theme Adjustments */
@media (prefers-color-scheme: dark) {
  .article-card-featured {
    --md-elevated-card-container-color: var(--md-sys-color-primary-container);
    border-color: var(--md-sys-color-primary);
  }
}
