/* Dashboard Styles */

.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
}

/* Loading and Error States */
.dashboard-loading,
.dashboard-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;
}

.dashboard-error .error-icon {
  font-size: 48px;
  color: var(--md-sys-color-error);
}

.dashboard-error h2 {
  color: var(--md-sys-color-on-surface);
  margin: 0;
}

.dashboard-error p {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
  text-align: center;
}

/* Welcome Section */
.dashboard-welcome {
  margin-bottom: 32px;
}

.dashboard-welcome h1 {
  color: var(--md-sys-color-on-surface);
  margin: 0 0 8px 0;
  font-weight: 500;
}

.dashboard-welcome p {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 48px;
}

.stat-card {
  --md-elevated-card-container-shape: 16px;
  cursor: default;
}

.stat-card-content {
  padding: 24px;
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  font-size: 32px;
  padding: 8px;
  border-radius: 12px;
}

.stat-card-primary .stat-icon {
  color: var(--md-sys-color-primary);
  background: var(--md-sys-color-primary-container);
}

.stat-card-secondary .stat-icon {
  color: var(--md-sys-color-secondary);
  background: var(--md-sys-color-secondary-container);
}

.stat-card-tertiary .stat-icon {
  color: var(--md-sys-color-tertiary);
  background: var(--md-sys-color-tertiary-container);
}

.stat-card-error .stat-icon {
  color: var(--md-sys-color-error);
  background: var(--md-sys-color-error-container);
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.positive {
  color: var(--md-sys-color-primary);
  background: var(--md-sys-color-primary-container);
}

.stat-trend.negative {
  color: var(--md-sys-color-error);
  background: var(--md-sys-color-error-container);
}

.stat-trend md-icon {
  font-size: 16px;
}

.stat-body {
  text-align: left;
}

.stat-value {
  color: var(--md-sys-color-on-surface);
  margin: 0 0 4px 0;
  font-weight: 600;
}

.stat-title {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
  font-weight: 500;
}

/* Quick Actions Section */
.quick-actions-section {
  margin-bottom: 48px;
}

.quick-actions-section h2 {
  color: var(--md-sys-color-on-surface);
  margin: 0 0 24px 0;
  font-weight: 500;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.quick-action-card {
  --md-outlined-card-container-shape: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-action-card:hover {
  --md-outlined-card-container-elevation: var(--md-sys-elevation-level2);
  transform: translateY(-2px);
}

.quick-action-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

.action-icon {
  font-size: 32px;
  padding: 12px;
  border-radius: 12px;
}

.action-icon-primary {
  color: var(--md-sys-color-primary);
  background: var(--md-sys-color-primary-container);
}

.action-icon-secondary {
  color: var(--md-sys-color-secondary);
  background: var(--md-sys-color-secondary-container);
}

.action-icon-tertiary {
  color: var(--md-sys-color-tertiary);
  background: var(--md-sys-color-tertiary-container);
}

.action-icon-error {
  color: var(--md-sys-color-error);
  background: var(--md-sys-color-error-container);
}

.action-text {
  flex: 1;
}

.action-text h3 {
  color: var(--md-sys-color-on-surface);
  margin: 0 0 4px 0;
  font-weight: 500;
}

.action-text p {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

.action-arrow {
  color: var(--md-sys-color-on-surface-variant);
  font-size: 20px;
  transition: transform 0.2s ease;
}

.quick-action-card:hover .action-arrow {
  transform: translateX(4px);
}

/* Recent Activity Section */
.recent-activity-section h2 {
  color: var(--md-sys-color-on-surface);
  margin: 0 0 24px 0;
  font-weight: 500;
}

.activity-card {
  --md-outlined-card-container-shape: 16px;
}

.activity-content {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 32px;
  text-align: center;
  justify-content: center;
}

.activity-icon {
  font-size: 48px;
  color: var(--md-sys-color-on-surface-variant);
  opacity: 0.6;
}

.activity-text p:first-child {
  color: var(--md-sys-color-on-surface);
  margin: 0 0 4px 0;
  font-weight: 500;
}

.activity-text p:last-child {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-action-content {
    padding: 16px;
  }
  
  .activity-content {
    flex-direction: column;
    text-align: center;
    padding: 24px;
  }
  
  .dashboard-welcome h1 {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .stat-card-content {
    padding: 16px;
  }
  
  .stat-icon {
    font-size: 24px;
    padding: 6px;
  }
  
  .action-icon {
    font-size: 24px;
    padding: 8px;
  }
}
