:root {
  font-family: "<PERSON>o", system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: var(--md-sys-color-on-background);
  background-color: var(--md-sys-color-background);

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: var(--md-sys-color-primary);
  text-decoration: inherit;
}
a:hover {
  color: var(--md-sys-color-primary-container);
}

body {
  margin: 0;
  /* display: flex; */
  /* place-items: center; */
  min-width: 320px;
  min-height: 100vh;
  overflow-x: hidden;
}

/* Hide scrollbars globally */
html {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

html::-webkit-scrollbar {
  display: none; /* WebKit */
}

body::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Hide scrollbars for all elements */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
  display: none; /* WebKit */
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

/* Global button styles removed to allow Material Design Web Components to use their default styles */

/* Material Design 3 theme variables are already defined in theme.css */
/* No need for manual light theme overrides */
