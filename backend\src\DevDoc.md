# Backend 开发文档

## 功能模块实现

### 1. Post 功能实现

#### 数据库字段设计

| 字段名      | 类型      | 说明         |
| ----------- | --------- | ------------ |
| id          | uint64    | 主键 ID      |
| title       | string    | 文章标题     |
| cover_url   | string    | 封面图片 URL |
| content     | string    | 文章内容     |
| category_id | uint64    | 目录 ID      |
| status      | uint8     | 文章状态     |
| post_images | [string]  | 文章图片     |
| created_at  | timestamp | 创建时间     |
| updated_at  | timestamp | 更新时间     |

#### 文章与目录、标签关系

- **文章与目录**：每个文章只能属于一个目录，通过 category_id 字段关联
- **文章与标签**：每个文章可以有多个标签，通过文章标签关联表(post_tags)实现多对多关系

#### 统一状态定义

| 状态值 | 状态名    | 说明   | 适用模块    |
| ------ | --------- | ------ | ----------- |
| 0      | draft     | 草稿   | Post        |
| 1      | published | 已发布 | Post, Music |
| 2      | deleted   | 已删除 | Post, Music |
| 3      | private   | 私密   | Post        |

#### 核心功能

- **创建 post** - 新增文章
- **获取 post** - 查询文章
- **更新 post** - 修改文章
- **删除 post** - 删除文章

#### Post 图片处理功能

- 处理前端上传的图片文件
- 生成图片 ID
- 生成图片 URL
- 生成图片路径
- 生成图片文件名
- 生成图片文件扩展名

### 2. Music 功能实现

#### 数据库字段设计

| 字段名          | 类型      | 说明         |
| --------------- | --------- | ------------ |
| id              | uint64    | 主键 ID      |
| music_name      | string    | 音乐名称     |
| music_author    | string    | 音乐作者     |
| music_url       | string    | 音乐文件 URL |
| music_cover_url | string    | 音乐封面 URL |
| status          | uint8     | 音乐状态     |
| created_at      | timestamp | 创建时间     |
| updated_at      | timestamp | 更新时间     |

#### 核心功能

- **创建 music** - 新增音乐
- **获取 music** - 查询音乐
- **更新 music** - 修改音乐
- **删除 music** - 删除音乐

#### 音乐上传处理逻辑

- 生成音乐 ID
- 生成音乐 URL
- 生成音乐路径
- 生成音乐文件名
- 生成音乐文件扩展名

### 3. Response 功能实现

#### 基础 Response 结构

包含状态码、消息和数据字段的标准响应格式

#### 基础 Response List 结构

包含状态码、消息和数据列表的标准响应格式

### 4. Admin 功能实现

#### 管理员鉴权

- 鉴权是否为管理员
- 使用 token 登录

### 5. 配置功能实现

#### 配置项详情

##### 数据库配置

数据库连接 URL 配置

**环境变量**: `DATABASE_URL`

##### JWT 配置

JWT 签名密钥和管理员访问令牌配置

**环境变量**:

- `JWT_SECRET` - JWT 签名密钥
- `BLOG_ADMIN_TOKEN` - 管理员访问令牌

##### 服务器配置

服务器主机和端口配置

**环境变量**: `PORT` (默认: 3001)

##### AI 服务配置

DeepSeek API 密钥和地址配置

**环境变量**:

- `DEEPSEEK_API_KEY` - DeepSeek API 密钥
- `DEEPSEEK_API_URL` - DeepSeek API 地址

##### CORS 配置

跨域资源共享配置

**环境变量**: `CORS_ORIGINS` (逗号分隔的域名列表)

##### 存储配置

文件上传目录和大小限制配置

**环境变量**:

- `UPLOAD_DIR` - 文件上传目录
- `BLOG_DATA_DIR` - 博客数据目录
- `MAX_FILE_SIZE` - 最大文件大小 (字节)

## 详细实现逻辑

### 文件处理通用逻辑

#### 文件上传流程

1. **文件验证**

   - 检查文件大小是否超过限制
   - 验证文件类型是否允许
   - 检查文件内容完整性

2. **文件存储**

   - 生成唯一文件名（UUID + 时间戳）
   - 创建存储目录（如果不存在）
   - 保存文件到指定路径
   - 返回文件访问 URL

3. **数据库更新**
   - 记录文件信息到数据库
   - 更新相关记录的 URL 字段

#### 文件更新流程

1. **获取原文件信息**

   - 从数据库查询原文件路径
   - 验证文件是否存在

2. **删除原文件**

   - 检查原文件是否存在
   - 删除原文件（如果存在）
   - 记录删除操作日志

3. **上传新文件**

   - 执行标准文件上传流程
   - 更新数据库记录

4. **错误处理**
   - 如果新文件上传失败，尝试恢复原文件
   - 记录错误日志

#### 文件删除流程

1. **数据库查询**

   - 获取文件路径信息
   - 验证记录存在性

2. **物理文件删除**

   - 检查文件是否存在
   - 删除物理文件
   - 清理空目录

3. **数据库更新**
   - 删除数据库记录
   - 更新相关关联记录

### Post 功能详细实现

#### 创建 Post 实现逻辑

创建 Post 的完整流程包括数据验证、文件处理、数据库记录创建等步骤

#### 更新 Post 实现逻辑

更新 Post 的完整流程包括获取原记录、文件更新处理、数据库记录更新等步骤

#### 删除 Post 实现逻辑

删除 Post 的完整流程包括获取记录、删除相关文件、软删除数据库记录等步骤

### Music 功能详细实现

#### 创建 Music 实现逻辑

创建 Music 的完整流程包括数据验证、音乐文件处理、封面图片处理、数据库记录创建等步骤

#### 更新 Music 实现逻辑

更新 Music 的完整流程包括获取原记录、音乐文件更新、封面图片更新、数据库记录更新等步骤

#### 删除 Music 实现逻辑

删除 Music 的完整流程包括获取记录、删除音乐文件和封面文件、软删除数据库记录等步骤

### Download 功能详细实现

#### 数据库字段设计

| 字段名     | 类型      | 说明     |
| ---------- | --------- | -------- |
| id         | uint64    | 主键 ID  |
| file_name  | string    | 文件名   |
| file_url   | string    | 文件 URL |
| file_type  | string    | 文件类型 |
| file_size  | uint64    | 文件大小 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

#### 上传文件实现逻辑

上传文件的完整流程包括文件验证、文件信息生成、文件保存、数据库记录创建等步骤

#### 删除文件实现逻辑

删除文件的完整流程包括获取文件记录、删除物理文件、删除数据库记录等步骤

### 错误处理机制

#### 文件操作错误处理

文件操作的安全处理机制，包括错误日志记录和错误类型分类处理

#### 数据库事务处理

数据库事务的安全处理机制，包括事务开始、提交和回滚操作

## API 接口文档

### 认证说明

#### 管理员认证

管理员接口需要在请求头中包含认证 Token

### 公共 API

#### 获取 Post 列表

```
GET /api/post/list
```

**查询参数**:

- `page`: 页码 (可选，默认: 1)
- `page_size`: 每页数量 (可选，默认: 10)
- `category_id`: 目录 ID 过滤 (可选)
- `status`: 状态过滤 (可选)

**响应示例**:
包含状态码、消息和数据列表的标准响应格式

#### 通过 ID 获取 Post

```
GET /api/post/get/:id
```

**路径参数**:

- `id`: Post ID

**响应示例**:
包含状态码、消息和单个 Post 数据的标准响应格式

#### 获取 Music 列表

```
GET /api/music/list
```

**查询参数**:

- `page`: 页码 (可选，默认: 1)
- `page_size`: 每页数量 (可选，默认: 10)
- `status`: 状态过滤 (可选)

**响应示例**:
包含状态码、消息和 Music 数据列表的标准响应格式

#### 通过 ID 获取 Music

```
GET /api/music/get/:id
```

**路径参数**:

- `id`: Music ID

**响应示例**:
包含状态码、消息和单个 Music 数据的标准响应格式

### 管理员 API

#### 创建 Post

```
POST /api/post/create
```

**请求头**:
包含认证 Token 和内容类型

**请求体**:
包含文章标题、封面 URL、内容、目录 ID、文章图片等字段

**响应示例**:
包含状态码、消息和创建的 Post 数据的标准响应格式

#### 上传 Post 图片

```
POST /api/post/upload_post_image
```

**请求头**:
包含认证 Token 和文件上传类型

**请求体**:
包含图片文件

**响应示例**:
包含状态码、消息和图片 URL 信息的标准响应格式

#### 更新 Post

```
PUT /api/post/update/:id
```

**请求头**:
包含认证 Token 和内容类型

**请求体**:
包含可选的更新字段（标题、封面 URL、内容、目录 ID、文章图片等）

**实现逻辑**:

1. 验证 Post 是否存在
2. 如果提供了新的封面图片，删除原封面文件并上传新文件
3. 如果提供了新的文章图片，删除原图片文件并上传新文件
4. 更新数据库记录
5. 返回更新后的 Post 信息

**响应示例**:
包含状态码、消息和更新后的 Post 数据的标准响应格式

#### 更新 Post 封面

```
PUT /api/post/update_cover/:id
```

**请求头**:
包含认证 Token 和文件上传类型

**请求体**:
包含封面文件

**实现逻辑**:

1. 获取原 Post 记录
2. 删除原封面文件（如果存在）
3. 上传新封面文件
4. 更新数据库中的 cover_url 字段
5. 返回更新后的 Post 信息

**响应示例**:
包含状态码、消息和更新后的封面 URL 信息的标准响应格式

#### 删除 Post

```
DELETE /api/post/delete/:id
```

**请求头**:
包含认证 Token

**路径参数**:

- `id`: Post ID

**实现逻辑**:

1. 获取 Post 记录
2. 删除所有相关的图片文件（封面和文章图片）
3. 软删除数据库记录（设置 status 为 2）
4. 返回删除成功信息

**响应示例**:
包含状态码、消息和空数据的标准响应格式

#### 获取文章标签

```
GET /api/post/get_tags/:id
```

**路径参数**:

- `id`: Post ID

**响应示例**:
包含状态码、消息和文章标签列表的标准响应格式

#### 更新文章标签

```
PUT /api/post/update_tags/:id
```

**请求头**:
包含认证 Token 和内容类型

**请求体**:
包含标签 ID 列表

**实现逻辑**:

1. 验证 Post 是否存在
2. 验证所有标签 ID 是否有效
3. 删除原有的标签关联
4. 创建新的标签关联
5. 返回更新后的标签信息

**响应示例**:
包含状态码、消息和更新后的标签列表的标准响应格式

#### 创建 Music

```
POST /api/music/create
```

**请求头**:
包含认证 Token 和内容类型

**请求体**:
包含音乐名称、作者、音乐 URL、封面 URL 等字段

**响应示例**:
包含状态码、消息和创建的 Music 数据的标准响应格式

#### 上传 Music 文件

```
POST /api/music/upload_music
```

**请求头**:
包含认证 Token 和文件上传类型

**请求体**:
包含音乐文件

**响应示例**:
包含状态码、消息和音乐 URL 信息的标准响应格式

#### 上传 Music 封面

```
POST /api/music/upload_music_cover/:id
```

**请求头**:
包含认证 Token 和文件上传类型

**路径参数**:

- `id`: Music ID

**请求体**:
包含封面文件

**实现逻辑**:

1. 获取 Music 记录
2. 删除原封面文件（如果存在）
3. 上传新封面文件
4. 更新数据库中的 music_cover_url 字段
5. 返回更新后的 Music 信息

**响应示例**:
包含状态码、消息和更新后的封面 URL 信息的标准响应格式

#### 更新 Music

```
PUT /api/music/update/:id
```

**请求头**:
包含认证 Token 和内容类型

**请求体**:
包含可选的更新字段

**实现逻辑**:

1. 验证 Music 是否存在
2. 如果提供了新的音乐文件，删除原音乐文件并上传新文件
3. 如果提供了新的封面图片，删除原封面文件并上传新文件
4. 更新数据库记录
5. 返回更新后的 Music 信息

**响应示例**:
包含状态码、消息和更新后的 Music 数据的标准响应格式

#### 删除 Music

```
DELETE /api/music/delete/:id
```

**请求头**:
包含认证 Token

**路径参数**:

- `id`: Music ID

**实现逻辑**:

1. 获取 Music 记录
2. 删除音乐文件和封面文件
3. 软删除数据库记录（设置 status 为 2）
4. 返回删除成功信息

**响应示例**:
包含状态码、消息和空数据的标准响应格式

### Download 功能 API

#### 上传可下载文件

```
POST /api/download/upload_file
```

**请求头**:
包含认证 Token 和文件上传类型

**请求体**:
包含文件

**实现逻辑**:

1. 验证文件类型和大小
2. 生成唯一文件名
3. 保存文件到 download 目录
4. 记录文件信息到数据库
5. 返回文件信息

**响应示例**:
包含状态码、消息和文件信息的标准响应格式

#### 下载文件

```
GET /api/download/download_file/:id
```

**路径参数**:

- `id`: 文件 ID

**实现逻辑**:

1. 根据 ID 获取文件记录
2. 检查文件是否存在
3. 设置适当的响应头（Content-Type, Content-Disposition）
4. 返回文件流

**响应头**:
包含文件类型、下载文件名、文件大小等信息

#### 获取文件列表

```
GET /api/download/get_file_list
```

**查询参数**:

- `page`: 页码 (可选，默认: 1)
- `page_size`: 每页数量 (可选，默认: 10)

**响应示例**:
包含状态码、消息和文件列表数据的标准响应格式

#### 获取文件信息

```
GET /api/download/get_file/:id
```

**路径参数**:

- `id`: 文件 ID

**响应示例**:
包含状态码、消息和单个文件信息的标准响应格式

#### 删除文件

```
DELETE /api/download/delete_file/:id
```

**请求头**:
包含认证 Token

**路径参数**:

- `id`: 文件 ID

**实现逻辑**:

1. 获取文件记录
2. 删除物理文件
3. 删除数据库记录
4. 返回删除成功信息

**响应示例**:
包含状态码、消息和空数据的标准响应格式

### 目录管理功能

#### 数据库字段设计

| 字段名     | 类型      | 说明     |
| ---------- | --------- | -------- |
| id         | uint64    | 主键 ID  |
| name       | string    | 目录名称 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

#### 目录与文章关系

- 每个文章只能属于一个目录
- 目录可以包含多个文章
- 文章表通过 category_id 字段关联目录表

#### 创建目录

```
POST /api/category/create
```

**请求头**:
包含认证 Token 和内容类型

**请求体**:
包含目录名称字段

**实现逻辑**:

1. 验证目录名称是否重复
2. 创建目录记录
3. 返回创建的目录信息

**响应示例**:
包含状态码、消息和创建的目录数据的标准响应格式

#### 获取所有目录

```
GET /api/category/list
```

**查询参数**:

**响应示例**:
包含状态码、消息和目录列表数据的标准响应格式

#### 更新目录

```
PUT /api/category/update/:id
```

**请求头**:
包含认证 Token 和内容类型

**请求体**:
包含目录名称字段

**实现逻辑**:

1. 验证目录是否存在
2. 检查名称是否与其他目录重复
3. 更新目录信息
4. 返回更新后的目录信息

**响应示例**:
包含状态码、消息和更新后的目录数据的标准响应格式

#### 删除目录

```
DELETE /api/category/delete/:id
```

**请求头**:
包含认证 Token

**路径参数**:

- `id`: 目录 ID

**实现逻辑**:

1. 检查目录是否被文章使用
2. 如果被使用，返回错误信息
3. 如果未被使用，删除目录记录
4. 返回删除成功信息

**响应示例**:
包含状态码、消息和空数据的标准响应格式

### 标签管理功能

#### 数据库字段设计

| 字段名     | 类型      | 说明     |
| ---------- | --------- | -------- |
| id         | uint64    | 主键 ID  |
| name       | string    | 标签名称 |
| created_at | timestamp | 创建时间 |
| updated_at | timestamp | 更新时间 |

#### 标签与文章关系

- 每个文章可以有多个标签
- 每个标签可以关联多个文章
- 通过文章标签关联表(post_tags)实现多对多关系

#### 创建标签

```
POST /api/tag/create
```

**请求头**:
包含认证 Token 和内容类型

**请求体**:
包含标签名称字段

**实现逻辑**:

1. 验证标签名称是否重复
2. 创建标签记录
3. 返回创建的标签信息

**响应示例**:
包含状态码、消息和创建的标签数据的标准响应格式

#### 获取所有标签

```
GET /api/tag/list
```

**查询参数**:

**响应示例**:
包含状态码、消息和标签列表数据的标准响应格式

#### 更新标签

```
PUT /api/tag/update/:id
```

**请求头**:
包含认证 Token 和内容类型

**请求体**:
包含标签名称字段

**实现逻辑**:

1. 验证标签是否存在
2. 更新标签信息
3. 返回更新后的标签信息

**响应示例**:
包含状态码、消息和更新后的标签数据的标准响应格式

#### 删除标签

```
DELETE /api/tag/delete/:id
```

**请求头**:
包含认证 Token

**路径参数**:

- `id`: 标签 ID

**实现逻辑**:

1. 检查标签是否被文章使用
2. 如果被使用，返回错误信息
3. 如果未被使用，删除标签记录
4. 返回删除成功信息

**响应示例**:
包含状态码、消息和空数据的标准响应格式

### 目录和标签详细实现

#### 目录管理实现逻辑

目录管理的完整流程包括数据验证、重复检查、数据库操作等步骤

#### 标签管理实现逻辑

标签管理的完整流程包括数据验证、重复检查、数据库操作等步骤

#### 文章与目录、标签关联

文章与目录、标签关联的完整流程包括验证目录和标签存在性、创建关联记录、更新关联关系等步骤

### 数据库迁移脚本

#### 创建目录表

创建目录表的 SQL 脚本，包含主键、名称、时间戳等字段

#### 创建标签表

创建标签表的 SQL 脚本，包含主键、名称、时间戳等字段

#### 创建文章标签关联表

创建文章标签关联表的 SQL 脚本，包含主键、文章 ID、标签 ID、时间戳等字段

#### 更新文章表

为文章表添加目录 ID 字段的 SQL 脚本
