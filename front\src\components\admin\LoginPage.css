/* Admin Login Page Styles */

.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background: linear-gradient(
    135deg,
    var(--md-sys-color-primary-container) 0%,
    var(--md-sys-color-secondary-container) 100%
  );
}

.login-background {
  position: relative;
  width: 100%;
  max-width: 400px;
}

.login-card {
  background: var(--md-sys-color-surface);
  border-radius: 24px;
  padding: 32px;
  box-shadow: var(--md-sys-elevation-level3);
  border: 1px solid var(--md-sys-color-outline-variant);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-icon {
  font-size: 48px;
  color: var(--md-sys-color-primary);
  margin-bottom: 16px;
  display: block;
}

.login-header h1 {
  color: var(--md-sys-color-on-surface);
  margin: 0 0 8px 0;
  font-weight: 500;
}

.login-header p {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.login-error {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--md-sys-color-error-container);
  color: var(--md-sys-color-on-error-container);
  border-radius: 12px;
  border: 1px solid var(--md-sys-color-error);
}

.login-error md-icon {
  font-size: 20px;
  color: var(--md-sys-color-error);
}

.form-field {
  display: flex;
  flex-direction: column;
}

.login-input {
  width: 100%;
  --md-outlined-text-field-container-shape: 12px;
}

.login-actions {
  margin-top: 8px;
}

.login-button {
  width: 100%;
  height: 48px;
  --md-filled-button-container-shape: 12px;
  --md-filled-button-label-text-size: 16px;
  --md-filled-button-label-text-weight: 500;
}

.login-button[disabled] {
  opacity: 0.6;
  cursor: not-allowed;
}

.login-footer {
  margin-top: 24px;
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid var(--md-sys-color-outline-variant);
}

.login-footer p {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
  font-style: italic;
}

.login-loading {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  background: var(--md-sys-color-surface);
}

.login-loading p {
  color: var(--md-sys-color-on-surface-variant);
  margin: 0;
}

/* Responsive Design */
@media (max-width: 480px) {
  .login-container {
    padding: 8px;
  }
  
  .login-card {
    padding: 24px;
    border-radius: 16px;
  }
  
  .login-header {
    margin-bottom: 24px;
  }
  
  .login-icon {
    font-size: 40px;
    margin-bottom: 12px;
  }
}

/* Animation for form submission */
.login-button md-circular-progress {
  --md-circular-progress-size: 18px;
  --md-circular-progress-active-indicator-color: var(--md-sys-color-on-primary);
}

/* Focus states */
.login-input:focus-within {
  --md-outlined-text-field-outline-color: var(--md-sys-color-primary);
  --md-outlined-text-field-outline-width: 2px;
}

/* Error state for inputs */
.login-input.error {
  --md-outlined-text-field-outline-color: var(--md-sys-color-error);
  --md-outlined-text-field-label-text-color: var(--md-sys-color-error);
}

/* Hover effects */
.login-button:hover:not([disabled]) {
  --md-filled-button-container-elevation: var(--md-sys-elevation-level2);
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .login-container {
    background: linear-gradient(
      135deg,
      var(--md-sys-color-primary-container) 0%,
      var(--md-sys-color-secondary-container) 100%
    );
  }
}
